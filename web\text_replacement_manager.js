/* Copyright 2024 PDF.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { RenderingStates } from "./ui_utils.js";

/**
 * TextReplacementManager handles text replacement data passed via URL parameters
 * and renders replacement overlays on the PDF pages.
 */
class TextReplacementManager {
  #replacements = new Map(); // pageNumber -> replacements array
  #pageViews = new Map(); // pageNumber -> PDFPageView
  #enabled = false;

  constructor() {
    console.log('TextReplacementManager: Constructor called');
    this.#parseReplacementsFromURL();

    // Debug logging
    if (this.#enabled) {
      console.log('TextReplacementManager: Initialized with', this.#replacements.size, 'pages of replacements');
      for (const [pageNum, replacements] of this.#replacements) {
        console.log(`TextReplacementManager: Page ${pageNum} has ${replacements.length} replacements`);
      }
    } else {
      console.log('TextReplacementManager: No replacements found in URL');
    }
  }

  /**
   * Parse text replacement data from URL parameters
   * Expected format: ?textReplacements=[{"page":1,"originalText":"old","replacementText":"new","style":{"backgroundColor":"yellow"}}]
   */
  #parseReplacementsFromURL() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const replacementsParam = urlParams.get('textReplacements');
      
      if (replacementsParam) {
        const replacementsData = JSON.parse(decodeURIComponent(replacementsParam));
        
        if (Array.isArray(replacementsData)) {
          for (const replacement of replacementsData) {
            if (this.#isValidReplacement(replacement)) {
              const pageNum = replacement.page;
              if (!this.#replacements.has(pageNum)) {
                this.#replacements.set(pageNum, []);
              }
              this.#replacements.get(pageNum).push(replacement);
            }
          }
          this.#enabled = this.#replacements.size > 0;
        }
      }
    } catch (error) {
      console.error('TextReplacementManager: Error parsing URL parameters:', error);
    }
  }

  /**
   * Validate replacement object structure
   */
  #isValidReplacement(replacement) {
    return (
      replacement &&
      typeof replacement.page === 'number' &&
      replacement.page > 0 &&
      typeof replacement.originalText === 'string' &&
      replacement.originalText.length > 0 &&
      typeof replacement.replacementText === 'string'
    );
  }

  /**
   * Check if manager is enabled
   */
  get enabled() {
    return this.#enabled;
  }

  /**
   * Check if there are replacements for a specific page
   */
  hasReplacementsForPage(pageNumber) {
    return this.#replacements.has(pageNumber);
  }

  /**
   * Get replacements for a specific page
   */
  getReplacementsForPage(pageNumber) {
    return this.#replacements.get(pageNumber) || [];
  }

  /**
   * Register a page view for replacement rendering
   */
  registerPageView(pageNumber, pageView) {
    console.log(`TextReplacementManager: Registering page ${pageNumber}`);
    this.#pageViews.set(pageNumber, pageView);

    // Set up event listeners for this page
    this.#setupPageEventListeners(pageNumber, pageView);

    // If there are replacements for this page, render them
    if (this.hasReplacementsForPage(pageNumber)) {
      console.log(`TextReplacementManager: Found replacements for page ${pageNumber}, rendering...`);
      this.#renderReplacementsForPage(pageNumber);
    } else {
      console.log(`TextReplacementManager: No replacements for page ${pageNumber}`);
    }
  }

  /**
   * Set up event listeners for a page to handle transforms
   */
  #setupPageEventListeners(pageNumber, pageView) {
    if (!pageView.eventBus) return;

    // Listen for page scale changes
    const onScaleChange = () => {
      if (this.hasReplacementsForPage(pageNumber)) {
        this.updateReplacements(pageNumber);
      }
    };

    // Listen for various page events that might affect positioning
    pageView.eventBus.on('scalechanging', onScaleChange);
    pageView.eventBus.on('rotationchanging', onScaleChange);
    pageView.eventBus.on('resize', onScaleChange);
  }

  /**
   * Unregister a page view
   */
  unregisterPageView(pageNumber) {
    this.#pageViews.delete(pageNumber);
  }

  /**
   * Render replacements for a specific page
   */
  #renderReplacementsForPage(pageNumber) {
    const pageView = this.#pageViews.get(pageNumber);
    const replacements = this.getReplacementsForPage(pageNumber);
    
    if (!pageView || !replacements.length) {
      return;
    }

    // Wait for the page and text layer to be rendered
    if (pageView.renderingState !== RenderingStates.FINISHED || !pageView.textLayer) {
      // Listen for text layer rendered event
      const onTextLayerRendered = (evt) => {
        if (evt.pageNumber === pageNumber) {
          this.#createReplacementElements(pageView, replacements);
          // Remove the event listener after use
          pageView.eventBus.off('textlayerrendered', onTextLayerRendered);
        }
      };
      pageView.eventBus.on('textlayerrendered', onTextLayerRendered);
    } else {
      this.#createReplacementElements(pageView, replacements);
    }
  }

  /**
   * Create replacement DOM elements for a page
   */
  #createReplacementElements(pageView, replacements) {
    const container = pageView.div;
    const textLayer = pageView.textLayer;
    
    if (!textLayer || !textLayer.div) {
      console.warn('TextReplacementManager: Text layer not available for page', pageView.id);
      return;
    }

    // Remove existing text replacements
    const existingReplacements = container.querySelectorAll('.text-replacement');
    existingReplacements.forEach(el => el.remove());

    // Create replacement layer if it doesn't exist
    let replacementLayer = container.querySelector('.text-replacement-layer');
    if (!replacementLayer) {
      replacementLayer = document.createElement('div');
      replacementLayer.className = 'text-replacement-layer';
      replacementLayer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 3;
      `;
      container.appendChild(replacementLayer);
    }

    // Process each replacement
    for (const replacement of replacements) {
      this.#processTextReplacement(replacement, textLayer, replacementLayer, pageView);
    }
  }

  /**
   * Process a single text replacement
   */
  #processTextReplacement(replacement, textLayer, replacementLayer, pageView) {
    const { originalText, replacementText } = replacement;
    
    // Find text elements that contain the original text
    const textElements = this.#findTextElements(originalText, textLayer);
    
    if (textElements.length === 0) {
      console.warn(`TextReplacementManager: Original text "${originalText}" not found on page ${pageView.id}`);
      return;
    }

    // Create replacement elements for each found text element
    for (const textElement of textElements) {
      this.#createReplacementElement(textElement, replacement, replacementLayer);
    }
  }

  /**
   * Find text elements containing the target text
   * COMPLETELY REWRITTEN - New approach for PDF text matching
   */
  #findTextElements(targetText, textLayer) {
    console.log(`TextReplacementManager: Searching for text: "${targetText}"`);
    const textElements = [];
    const textDivs = Array.from(textLayer.div.querySelectorAll('span[role="presentation"]'));

    console.log(`TextReplacementManager: Found ${textDivs.length} text spans`);

    // Log first few spans for debugging
    for (let i = 0; i < Math.min(5, textDivs.length); i++) {
      const span = textDivs[i];
      console.log(`TextReplacementManager: Span ${i}: "${span.textContent}"`);
    }

    // First try simple single-span matching
    const singleSpanMatches = this.#findSingleSpanMatches(targetText, textDivs);
    if (singleSpanMatches.length > 0) {
      console.log(`TextReplacementManager: Found ${singleSpanMatches.length} single-span matches`);
      return singleSpanMatches;
    }

    // If no single-span matches, try multi-span matching with improved algorithm
    console.log('TextReplacementManager: Trying multi-span matching...');
    const multiSpanMatches = this.#findMultiSpanMatches(targetText, textDivs);
    console.log(`TextReplacementManager: Found ${multiSpanMatches.length} multi-span matches`);

    return multiSpanMatches;
  }

  /**
   * Find matches within single spans
   */
  #findSingleSpanMatches(targetText, textDivs) {
    const matches = [];
    const normalizedTarget = this.#normalizeText(targetText);

    for (const span of textDivs) {
      const spanText = span.textContent || '';
      const normalizedSpanText = this.#normalizeText(spanText);

      if (normalizedSpanText.includes(normalizedTarget)) {
        matches.push({
          element: span,
          text: spanText,
          targetText: targetText,
          spanGroup: [span],
          isMultiSpan: false
        });
        console.log(`TextReplacementManager: Single-span match found: "${spanText}"`);
      }
    }

    return matches;
  }

  /**
   * Find matches across multiple spans - COMPLETELY NEW ALGORITHM
   */
  #findMultiSpanMatches(targetText, textDivs) {
    const matches = [];
    const normalizedTarget = this.#normalizeText(targetText);

    console.log(`TextReplacementManager: Multi-span search for: "${normalizedTarget}"`);

    // Build complete text with span mapping
    let completeText = '';
    const spanMap = []; // Maps character positions to spans

    for (let i = 0; i < textDivs.length; i++) {
      const span = textDivs[i];
      const spanText = span.textContent || '';
      const normalizedSpanText = this.#normalizeText(spanText);

      if (normalizedSpanText.length > 0) {
        const startPos = completeText.length;
        completeText += normalizedSpanText;

        // Map each character to its span
        for (let j = 0; j < normalizedSpanText.length; j++) {
          spanMap.push({
            span: span,
            spanIndex: i,
            charIndex: j,
            originalText: spanText
          });
        }

        // Add space between spans if they appear to be separate words
        if (i < textDivs.length - 1) {
          const nextSpan = textDivs[i + 1];
          if (this.#shouldAddSpaceBetweenSpans(span, nextSpan)) {
            completeText += ' ';
            spanMap.push({
              span: null, // Space character
              spanIndex: -1,
              charIndex: -1,
              originalText: ' '
            });
          }
        }
      }
    }

    console.log(`TextReplacementManager: Built complete text (${completeText.length} chars): "${completeText.substring(0, 100)}..."`);

    // Find all occurrences of target text
    let searchStart = 0;
    while (true) {
      const matchIndex = completeText.indexOf(normalizedTarget, searchStart);
      if (matchIndex === -1) break;

      const matchEnd = matchIndex + normalizedTarget.length;
      console.log(`TextReplacementManager: Found match at position ${matchIndex}-${matchEnd}`);

      // Find which spans contain this match
      const matchSpans = new Set();
      let matchedText = '';

      for (let i = matchIndex; i < matchEnd && i < spanMap.length; i++) {
        const mapping = spanMap[i];
        if (mapping.span) {
          matchSpans.add(mapping.span);
          if (matchedText.length === 0 || !matchedText.includes(mapping.originalText)) {
            matchedText += mapping.originalText;
          }
        }
      }

      if (matchSpans.size > 0) {
        const spanArray = Array.from(matchSpans);
        matches.push({
          element: spanArray[0], // First span as reference
          text: matchedText,
          targetText: targetText,
          spanGroup: spanArray,
          isMultiSpan: spanArray.length > 1,
          matchStart: matchIndex,
          matchEnd: matchEnd
        });

        console.log(`TextReplacementManager: Multi-span match created with ${spanArray.length} spans: "${matchedText}"`);
      }

      searchStart = matchIndex + 1;
    }

    return matches;
  }



  /**
   * Determine if space should be added between two spans
   */
  #shouldAddSpaceBetweenSpans(span1, span2) {
    if (!span1 || !span2) return false;

    try {
      const rect1 = span1.getBoundingClientRect();
      const rect2 = span2.getBoundingClientRect();

      // If spans are on different lines, add space
      if (Math.abs(rect1.top - rect2.top) > 5) {
        return true;
      }

      // If there's a significant horizontal gap, add space
      if (Math.abs(rect2.left - rect1.right) > 3) {
        return true;
      }

      return false;
    } catch (e) {
      // Fallback: add space between different spans
      return true;
    }
  }



  /**
   * Normalize text for comparison (handle spaces, line breaks, etc.)
   */
  #normalizeText(text) {
    return text
      .replace(/\s+/g, ' ')  // Replace multiple spaces with single space
      .replace(/\u00A0/g, ' ')  // Replace non-breaking spaces
      .replace(/[\r\n\t]/g, ' ')  // Replace line breaks and tabs
      .trim()
      .toLowerCase();
  }



  /**
   * Create a replacement element for a text element
   */
  #createReplacementElement(textElement, replacement, replacementLayer) {
    const { element, targetText, spanGroup, isMultiSpan } = textElement;
    const { replacementText, style = {} } = replacement;

    // Calculate position and size based on element type
    let position, dimensions;
    if (isMultiSpan && spanGroup) {
      position = this.#calculateMultiSpanPosition(spanGroup, replacementLayer);
      dimensions = this.#calculateMultiSpanDimensions(spanGroup);
    } else {
      position = this.#calculateElementPosition(element, replacementLayer);
      dimensions = this.#calculateElementDimensions(element);
    }

    const { left, top } = position;
    const { width, height } = dimensions;

    // Create overlay element(s) to cover original text
    let overlay = null;
    if (isMultiSpan) {
      this.#createMultiSpanOverlays(spanGroup, replacementLayer, style);
    } else {
      // Create single overlay element to cover original text
      overlay = document.createElement('div');
      overlay.className = 'text-replacement text-replacement-overlay';
      overlay.style.cssText = `
        position: absolute;
        left: ${left}px;
        top: ${top}px;
        width: ${width}px;
        height: ${height}px;
        background-color: #ffffff;
        z-index: 1;
        pointer-events: none;
      `;
      replacementLayer.appendChild(overlay);
    }

    // Create replacement text element
    const replacementElement = document.createElement('div');
    replacementElement.className = 'text-replacement text-replacement-text';
    replacementElement.textContent = replacementText;

    // Copy font styles from original element
    const computedStyle = window.getComputedStyle(element);

    // Calculate appropriate font size for the replacement text
    const originalFontSize = parseFloat(computedStyle.fontSize);
    const adjustedFontSize = this.#calculateOptimalFontSize(
      replacementText,
      targetText,
      originalFontSize,
      width,
      computedStyle.fontFamily
    );

    // Special handling for multi-span text (likely multi-line)
    const isMultiLine = isMultiSpan && height > originalFontSize * 1.5;

    replacementElement.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      font-family: ${style.fontFamily || computedStyle.fontFamily};
      font-size: ${style.fontSize || adjustedFontSize + 'px'};
      font-weight: ${style.fontWeight || computedStyle.fontWeight};
      color: ${style.color || computedStyle.color};
      background-color: ${style.backgroundColor || 'rgba(255, 255, 0, 0.3)'};
      border: ${style.border || '1px solid rgba(255, 255, 0, 0.8)'};
      border-radius: 2px;
      padding: 0;
      margin: 0;
      line-height: ${isMultiLine ? '1.2' : computedStyle.lineHeight};
      text-align: ${computedStyle.textAlign};
      z-index: 2;
      pointer-events: none;
      box-sizing: border-box;
      display: flex;
      align-items: ${isMultiLine ? 'flex-start' : 'center'};
      justify-content: ${computedStyle.textAlign === 'center' ? 'center' : 'flex-start'};
      overflow: hidden;
      white-space: ${isMultiLine ? 'normal' : 'nowrap'};
      word-wrap: break-word;
      ${isMultiLine ? '' : 'text-overflow: ellipsis;'}
    `;

    // Add elements to replacement layer (overlay already added for multi-span)
    if (!isMultiSpan) {
      // Overlay already added above for single span
    }
    replacementLayer.appendChild(replacementElement);

    // Add data attributes for identification
    if (overlay) {
      overlay.setAttribute('data-page', replacement.page);
      overlay.setAttribute('data-original-text', targetText);
    }
    replacementElement.setAttribute('data-page', replacement.page);
    replacementElement.setAttribute('data-original-text', targetText);
    replacementElement.setAttribute('data-replacement-text', replacementText);
  }

  /**
   * Calculate position of a single element relative to replacement layer
   * FIXED - Using PDF.js compatible coordinate system
   */
  #calculateElementPosition(element, replacementLayer) {
    try {
      console.log('TextReplacementManager: Calculating position for element:', element.textContent);

      // Get the page container and text layer to understand the coordinate system
      const pageContainer = replacementLayer.closest('.page');
      const textLayerDiv = element.closest('.textLayer');

      if (!pageContainer) {
        console.warn('TextReplacementManager: No page container found');
        return { left: 0, top: 0 };
      }

      // Get element's absolute position
      const elementRect = element.getBoundingClientRect();
      const pageRect = pageContainer.getBoundingClientRect();

      // Get text layer offset if it exists
      let textLayerOffset = { left: 0, top: 0 };
      if (textLayerDiv) {
        const textLayerRect = textLayerDiv.getBoundingClientRect();
        textLayerOffset = {
          left: textLayerRect.left - pageRect.left,
          top: textLayerRect.top - pageRect.top
        };
      }

      // Calculate position relative to replacement layer instead of page container
      const replacementLayerRect = replacementLayer.getBoundingClientRect();

      const position = {
        left: elementRect.left - replacementLayerRect.left,
        top: elementRect.top - replacementLayerRect.top
      };

      console.log(`TextReplacementManager: Element "${element.textContent}" position calculation:`, {
        elementRect: {
          left: elementRect.left.toFixed(2),
          top: elementRect.top.toFixed(2),
          width: elementRect.width.toFixed(2),
          height: elementRect.height.toFixed(2)
        },
        pageRect: {
          left: pageRect.left.toFixed(2),
          top: pageRect.top.toFixed(2),
          width: pageRect.width.toFixed(2),
          height: pageRect.height.toFixed(2)
        },
        replacementLayerRect: {
          left: replacementLayerRect.left.toFixed(2),
          top: replacementLayerRect.top.toFixed(2),
          width: replacementLayerRect.width.toFixed(2),
          height: replacementLayerRect.height.toFixed(2)
        },
        textLayerOffset: {
          left: textLayerOffset.left.toFixed(2),
          top: textLayerOffset.top.toFixed(2)
        },
        calculatedPosition: {
          left: position.left.toFixed(2),
          top: position.top.toFixed(2)
        }
      });

      return position;

    } catch (error) {
      console.error('TextReplacementManager: Error calculating element position:', error);
      return { left: 0, top: 0 };
    }
  }

  /**
   * Calculate dimensions of a single element
   */
  #calculateElementDimensions(element) {
    const rect = element.getBoundingClientRect();
    return {
      width: rect.width,
      height: rect.height
    };
  }

  /**
   * Calculate position for multi-span text
   * FIXED - Using PDF.js compatible coordinate system
   */
  #calculateMultiSpanPosition(spanGroup, replacementLayer) {
    if (!spanGroup || spanGroup.length === 0) {
      return { left: 0, top: 0 };
    }

    try {
      console.log(`TextReplacementManager: Calculating multi-span position for ${spanGroup.length} spans`);

      // Get the page container
      const pageContainer = replacementLayer.closest('.page');
      if (!pageContainer) {
        console.warn('TextReplacementManager: No page container found for multi-span');
        return { left: 0, top: 0 };
      }

      // Calculate the bounding box of all spans
      const boundingBox = this.#getMultiSpanBoundingBox(spanGroup);
      const pageRect = pageContainer.getBoundingClientRect();

      const position = {
        left: boundingBox.left - pageRect.left,
        top: boundingBox.top - pageRect.top
      };

      console.log(`TextReplacementManager: Multi-span position calculation:`, {
        boundingBox: {
          left: boundingBox.left.toFixed(2),
          top: boundingBox.top.toFixed(2),
          width: boundingBox.width.toFixed(2),
          height: boundingBox.height.toFixed(2)
        },
        pageRect: {
          left: pageRect.left.toFixed(2),
          top: pageRect.top.toFixed(2)
        },
        calculatedPosition: {
          left: position.left.toFixed(2),
          top: position.top.toFixed(2)
        },
        spanCount: spanGroup.length
      });

      return position;

    } catch (error) {
      console.error('TextReplacementManager: Error calculating multi-span position:', error);
      return this.#calculateElementPosition(spanGroup[0], replacementLayer);
    }
  }

  /**
   * Calculate dimensions for multi-span text
   */
  #calculateMultiSpanDimensions(spanGroup) {
    if (!spanGroup || spanGroup.length === 0) {
      return { width: 0, height: 0 };
    }

    if (spanGroup.length === 1) {
      return this.#calculateElementDimensions(spanGroup[0]);
    }

    const boundingBox = this.#getMultiSpanBoundingBox(spanGroup);
    return {
      width: boundingBox.width,
      height: boundingBox.height
    };
  }

  /**
   * Get accurate bounding box for multiple spans
   * Improved algorithm for better multi-span handling
   */
  #getMultiSpanBoundingBox(spanGroup) {
    if (!spanGroup || spanGroup.length === 0) {
      return { left: 0, top: 0, right: 0, bottom: 0, width: 0, height: 0 };
    }

    if (spanGroup.length === 1) {
      const rect = spanGroup[0].getBoundingClientRect();
      return {
        left: rect.left,
        top: rect.top,
        right: rect.right,
        bottom: rect.bottom,
        width: rect.width,
        height: rect.height
      };
    }

    // For multi-span text, we need to handle line breaks properly
    // Group spans by their vertical position (line)
    const spansByLine = this.#groupSpansByLine(spanGroup);

    let minLeft = Infinity, minTop = Infinity;
    let maxRight = -Infinity, maxBottom = -Infinity;

    // Calculate bounding box for each line and combine
    for (const lineSpans of spansByLine) {
      for (const span of lineSpans) {
        try {
          const rect = span.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) { // Only consider visible spans
            minLeft = Math.min(minLeft, rect.left);
            minTop = Math.min(minTop, rect.top);
            maxRight = Math.max(maxRight, rect.right);
            maxBottom = Math.max(maxBottom, rect.bottom);
          }
        } catch (error) {
          console.warn('TextReplacementManager: Error getting span rect:', error);
        }
      }
    }

    // Fallback if no valid rects found
    if (minLeft === Infinity) {
      const firstRect = spanGroup[0].getBoundingClientRect();
      return {
        left: firstRect.left,
        top: firstRect.top,
        right: firstRect.right,
        bottom: firstRect.bottom,
        width: firstRect.width,
        height: firstRect.height
      };
    }

    return {
      left: minLeft,
      top: minTop,
      right: maxRight,
      bottom: maxBottom,
      width: maxRight - minLeft,
      height: maxBottom - minTop
    };
  }

  /**
   * Group spans by their vertical position (line)
   */
  #groupSpansByLine(spanGroup) {
    const lines = [];
    const lineThreshold = 5; // pixels tolerance for same line

    for (const span of spanGroup) {
      const rect = span.getBoundingClientRect();
      let addedToLine = false;

      // Try to add to existing line
      for (const line of lines) {
        const lineRect = line[0].getBoundingClientRect();
        if (Math.abs(rect.top - lineRect.top) <= lineThreshold) {
          line.push(span);
          addedToLine = true;
          break;
        }
      }

      // Create new line if not added to existing
      if (!addedToLine) {
        lines.push([span]);
      }
    }

    // Sort lines by vertical position
    lines.sort((a, b) => {
      const aRect = a[0].getBoundingClientRect();
      const bRect = b[0].getBoundingClientRect();
      return aRect.top - bRect.top;
    });

    // Sort spans within each line by horizontal position
    for (const line of lines) {
      line.sort((a, b) => {
        const aRect = a.getBoundingClientRect();
        const bRect = b.getBoundingClientRect();
        return aRect.left - bRect.left;
      });
    }

    return lines;
  }

  /**
   * Calculate optimal font size for replacement text to fit in the original text area
   */
  #calculateOptimalFontSize(replacementText, originalText, originalFontSize, availableWidth, fontFamily) {
    // If replacement text is shorter or same length, use original font size
    if (replacementText.length <= originalText.length) {
      return originalFontSize;
    }

    // Create a temporary element to measure text width
    const tempElement = document.createElement('span');
    tempElement.style.cssText = `
      position: absolute;
      visibility: hidden;
      white-space: nowrap;
      font-family: ${fontFamily};
      font-size: ${originalFontSize}px;
      top: -9999px;
      left: -9999px;
    `;
    tempElement.textContent = replacementText;
    document.body.appendChild(tempElement);

    const textWidth = tempElement.getBoundingClientRect().width;
    document.body.removeChild(tempElement);

    // For very long text, be more aggressive in scaling
    const lengthRatio = replacementText.length / originalText.length;
    let scaleFactor;

    if (lengthRatio > 3) {
      // Very long replacement text - scale more aggressively
      scaleFactor = Math.min(1, (availableWidth - 8) / textWidth);
    } else if (lengthRatio > 1.5) {
      // Moderately long replacement text
      scaleFactor = Math.min(1, (availableWidth - 6) / textWidth);
    } else {
      // Slightly longer replacement text
      scaleFactor = Math.min(1, (availableWidth - 4) / textWidth);
    }

    // Set minimum font size based on original size
    const minFontSize = Math.max(originalFontSize * 0.5, 8); // Minimum 50% or 8px
    const adjustedFontSize = Math.max(originalFontSize * scaleFactor, minFontSize);

    return adjustedFontSize;
  }

  /**
   * Update replacements when page is transformed (zoom, rotate, etc.)
   */
  updateReplacements(pageNumber) {
    if (!this.hasReplacementsForPage(pageNumber)) {
      return;
    }

    const pageView = this.#pageViews.get(pageNumber);
    if (!pageView) {
      return;
    }

    // Use requestAnimationFrame to ensure DOM has been updated after transform
    requestAnimationFrame(() => {
      // Double RAF to ensure layout is complete
      requestAnimationFrame(() => {
        const replacements = this.getReplacementsForPage(pageNumber);
        this.#createReplacementElements(pageView, replacements);
      });
    });
  }

  /**
   * Add new replacements programmatically
   */
  addReplacements(newReplacements) {
    console.log('TextReplacementManager: addReplacements called with', newReplacements);

    if (!Array.isArray(newReplacements)) {
      console.warn('TextReplacementManager: Invalid replacements array');
      return;
    }

    for (const replacement of newReplacements) {
      console.log('TextReplacementManager: Processing replacement:', replacement);
      if (this.#isValidReplacement(replacement)) {
        const pageNum = replacement.page;
        if (!this.#replacements.has(pageNum)) {
          this.#replacements.set(pageNum, []);
        }
        this.#replacements.get(pageNum).push(replacement);
        console.log(`TextReplacementManager: Added replacement for page ${pageNum}`);

        // Render if page is already loaded
        if (this.#pageViews.has(pageNum)) {
          console.log(`TextReplacementManager: Page ${pageNum} is loaded, rendering replacements`);
          this.#renderReplacementsForPage(pageNum);
        } else {
          console.log(`TextReplacementManager: Page ${pageNum} not yet loaded`);
        }
      } else {
        console.warn('TextReplacementManager: Invalid replacement:', replacement);
      }
    }

    this.#enabled = this.#replacements.size > 0;
    console.log(`TextReplacementManager: Enabled: ${this.#enabled}, Total pages: ${this.#replacements.size}`);
  }

  /**
   * Clear all replacements
   */
  clearReplacements() {
    // Remove all replacement elements from DOM
    for (const [pageNumber] of this.#pageViews) {
      const pageView = this.#pageViews.get(pageNumber);
      if (pageView) {
        const container = pageView.div;
        const existingReplacements = container.querySelectorAll('.text-replacement');
        existingReplacements.forEach(el => el.remove());
      }
    }

    this.#replacements.clear();
    this.#enabled = false;
  }

  /**
   * Create multiple overlay elements for multi-span text to ensure better coverage
   */
  #createMultiSpanOverlays(spanGroup, replacementLayer, style) {
    const pageContainer = replacementLayer.closest('.page');
    if (!pageContainer) {
      console.warn('TextReplacementManager: No page container found for multi-span overlays');
      return;
    }

    const pageRect = pageContainer.getBoundingClientRect();
    const spansByLine = this.#groupSpansByLine(spanGroup);

    // Create overlay for each line of text
    for (const lineSpans of spansByLine) {
      let minLeft = Infinity, minTop = Infinity;
      let maxRight = -Infinity, maxBottom = -Infinity;

      // Calculate bounding box for this line
      for (const span of lineSpans) {
        const rect = span.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          minLeft = Math.min(minLeft, rect.left);
          minTop = Math.min(minTop, rect.top);
          maxRight = Math.max(maxRight, rect.right);
          maxBottom = Math.max(maxBottom, rect.bottom);
        }
      }

      if (minLeft !== Infinity) {
        // Convert to page-relative coordinates
        const left = minLeft - pageRect.left;
        const top = minTop - pageRect.top;
        const width = maxRight - minLeft;
        const height = maxBottom - minTop;

        // Create overlay for this line
        const overlay = document.createElement('div');
        overlay.className = 'text-replacement text-replacement-overlay';
        overlay.style.cssText = `
          position: absolute;
          left: ${left}px;
          top: ${top}px;
          width: ${width}px;
          height: ${height}px;
          background-color: #ffffff;
          z-index: 1;
          pointer-events: none;
        `;
        replacementLayer.appendChild(overlay);
      }
    }
  }
}

export { TextReplacementManager };
