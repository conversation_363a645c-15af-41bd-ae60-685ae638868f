<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>PDF with Full Features</title>
</head>
<body>
    <h1>PDF查看器（包含文本替换预览功能）</h1>

    <button onclick="loadPDFWithFullFeatures()">加载带文本替换的PDF</button>
    <button onclick="loadDemoReplacements()">加载演示文本替换</button>
    
    <iframe 
        id="pdfViewer" 
        width="100%" 
        height="600px" 
        style="border: 1px solid #ccc;"
        sandbox="allow-scripts allow-same-origin allow-forms allow-downloads">
    </iframe>

    <script>
        function loadPDFWithFullFeatures() {
            const pdfFile = 'compressed.tracemonkey-pldi-09.pdf';

            // 高亮配置
            const highlights = [{
                page: 1,
                x1: 100, y1: 200, x2: 400, y2: 250,
                colorClass: 'yellow'
            }];

            // 文本替换配置
            const textReplacements = [
                {
                    page: 1,
                    originalText: "JavaScript",
                    replacementText: "TypeScript",
                    style: {
                        backgroundColor: "rgba(255, 255, 0, 0.3)",
                        color: "#000000",
                        border: "1px solid rgba(255, 255, 0, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "Mozilla",
                    replacementText: "OpenAI",
                    style: {
                        backgroundColor: "rgba(0, 255, 0, 0.3)",
                        color: "#000000",
                        border: "1px solid rgba(0, 255, 0, 0.8)"
                    }
                }
            ];

            const encodedHighlights = encodeURIComponent(JSON.stringify(highlights));
            const encodedTextReplacements = encodeURIComponent(JSON.stringify(textReplacements));

            // 包含所有必要参数的URL
            const url = `build/generic/web/viewer.html?` +
                `file=${pdfFile}&` +
                `highlights=${encodedHighlights}&` +
                `textReplacements=${encodedTextReplacements}`

            document.getElementById('pdfViewer').src = url;
        }

        // 通过postMessage接收文本替换配置
        function receiveTextReplacements(event) {
            // 验证来源（在实际应用中应该验证origin）
            if (event.data && event.data.type === 'textReplacements') {
                const { replacements, pdfFile } = event.data;
                loadPDFWithTextReplacements(pdfFile || 'compressed.tracemonkey-pldi-09.pdf', replacements);
            }
        }

        // 使用指定的文本替换配置加载PDF
        function loadPDFWithTextReplacements(pdfFile, textReplacements) {
            const encodedTextReplacements = encodeURIComponent(JSON.stringify(textReplacements));

            const url = `build/generic/web/viewer.html?` +
                `file=${pdfFile}&` +
                `textReplacements=${encodedTextReplacements}`;

            document.getElementById('pdfViewer').src = url;
        }

        // 从URL参数获取文本替换配置
        function loadFromURLParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const textReplacementsParam = urlParams.get('textReplacements');
            const pdfFileParam = urlParams.get('file');

            if (textReplacementsParam) {
                try {
                    const textReplacements = JSON.parse(decodeURIComponent(textReplacementsParam));
                    loadPDFWithTextReplacements(
                        pdfFileParam || 'compressed.tracemonkey-pldi-09.pdf',
                        textReplacements
                    );
                    return true;
                } catch (error) {
                    console.error('Error parsing text replacements from URL:', error);
                }
            }
            return false;
        }

        // 加载演示文本替换
        function loadDemoReplacements() {
            const demoReplacements = [
                {
                    page: 1,
                    originalText: "trace",
                    replacementText: "追踪",
                    style: {
                        backgroundColor: "rgba(255, 192, 203, 0.3)",
                        color: "#000000",
                        border: "1px solid rgba(255, 192, 203, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "monkey",
                    replacementText: "猴子",
                    style: {
                        backgroundColor: "rgba(0, 150, 255, 0.3)",
                        color: "#ffffff",
                        border: "1px solid rgba(0, 150, 255, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "compilation",
                    replacementText: "编译",
                    style: {
                        backgroundColor: "rgba(128, 0, 128, 0.3)",
                        color: "#ffffff",
                        border: "1px solid rgba(128, 0, 128, 0.8)"
                    }
                }
            ];

            loadPDFWithTextReplacements('compressed.tracemonkey-pldi-09.pdf', demoReplacements);
        }

        // 页面加载时的初始化
        window.onload = function() {
            // 监听postMessage事件
            window.addEventListener('message', receiveTextReplacements, false);

            // 首先尝试从URL参数加载，如果没有则使用默认配置
            if (!loadFromURLParams()) {
                loadPDFWithFullFeatures();
            }
        };

        // 提供给外部调用的API
        window.PDFTextReplacementAPI = {
            // 设置文本替换
            setTextReplacements: function(pdfFile, textReplacements) {
                loadPDFWithTextReplacements(pdfFile, textReplacements);
            },

            // 添加单个文本替换
            addTextReplacement: function(replacement) {
                // 通过postMessage发送给iframe中的PDF查看器
                const iframe = document.getElementById('pdfViewer');
                if (iframe && iframe.contentWindow) {
                    iframe.contentWindow.postMessage({
                        type: 'addTextReplacement',
                        replacement: replacement
                    }, '*');
                }
            },

            // 清除所有文本替换
            clearTextReplacements: function() {
                const iframe = document.getElementById('pdfViewer');
                if (iframe && iframe.contentWindow) {
                    iframe.contentWindow.postMessage({
                        type: 'clearTextReplacements'
                    }, '*');
                }
            }
        };
    </script>
</body>
</html>