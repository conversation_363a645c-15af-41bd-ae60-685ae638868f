<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>长文本跨行替换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 4px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .test-case h4 {
            margin: 0 0 5px 0;
            color: #007bff;
        }
        .test-case code {
            background-color: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PDF.js 长文本跨行替换测试</h1>
        
        <div class="test-case">
            <h4>测试用例说明</h4>
            <p>以下测试用例专门针对长文本和跨行文本的替换功能：</p>
            <ul>
                <li><strong>短句替换</strong>：测试基本的短文本替换功能</li>
                <li><strong>长句替换</strong>：测试跨多个span元素的长文本替换</li>
                <li><strong>跨行替换</strong>：测试跨越多行的超长文本替换</li>
                <li><strong>段落替换</strong>：测试完整段落的替换</li>
            </ul>
        </div>

        <div class="controls">
            <button onclick="testShortText()">短句替换测试</button>
            <button class="btn-success" onclick="testLongText()">长句替换测试</button>
            <button class="btn-warning" onclick="testMultiLineText()">跨行替换测试</button>
            <button class="btn-danger" onclick="testParagraphText()">段落替换测试</button>
            <button onclick="testCombined()">组合测试</button>
            <button onclick="clearAll()">清除所有</button>
            <button onclick="loadOriginal()">原始PDF</button>
        </div>

        <div id="status" class="status">
            准备就绪，请选择测试功能
        </div>

        <iframe id="pdfViewer" src="about:blank"></iframe>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function testShortText() {
            updateStatus('测试短句替换：JavaScript → JS');
            
            const textReplacements = [
                {
                    page: 1,
                    originalText: "JavaScript",
                    replacementText: "JS",
                    style: {
                        backgroundColor: "rgba(255, 255, 0, 0.8)",
                        color: "#000000",
                        border: "2px solid #ff0000"
                    }
                }
            ];
            
            loadPDFWithReplacements(textReplacements);
        }

        function testLongText() {
            updateStatus('测试长句替换：Dynamic languages such as JavaScript → 动态语言如JavaScript');
            
            const textReplacements = [
                {
                    page: 1,
                    originalText: "Dynamic languages such as JavaScript",
                    replacementText: "动态语言如JavaScript",
                    style: {
                        backgroundColor: "rgba(0, 255, 0, 0.6)",
                        color: "#000000",
                        border: "2px solid #00ff00"
                    }
                }
            ];
            
            loadPDFWithReplacements(textReplacements);
        }

        function testMultiLineText() {
            updateStatus('测试跨行替换：expect hot loops to be mostly type-stable...');
            
            const textReplacements = [
                {
                    page: 1,
                    originalText: "expect hot loops to be mostly type-stable, meaning that the types of values are invariant. (12) For example, we would expect loop coun-ters that start as integers to remain integers for all iterations. When",
                    replacementText: "期望热循环大部分是类型稳定的，这意味着值的类型是不变的。例如，我们期望以整数开始的循环计数器在所有迭代中都保持为整数。当",
                    style: {
                        backgroundColor: "rgba(255, 0, 255, 0.5)",
                        color: "#ffffff",
                        border: "2px dashed #ff00ff"
                    }
                }
            ];
            
            loadPDFWithReplacements(textReplacements);
        }

        function testParagraphText() {
            updateStatus('测试段落替换：完整段落替换');
            
            const textReplacements = [
                {
                    page: 1,
                    originalText: "Categories and Subject Descriptors D.3.4 [Programming Lan-guages]: Processors",
                    replacementText: "像JavaScript这样的动态语言比静态类型语言更难编译。由于没有具体的类型信息可用，传统编译器需要发出能够在运行时处理所有可能类型组合的通用代码。我们提出了一种针对动态类型语言的替代编译技术，该技术在运行时识别频繁执行的循环轨迹，然后动态生成专门针对循环中每个路径上实际动态类型的机器代码。我们的",
                    style: {
                        backgroundColor: "rgba(0, 150, 255, 0.4)",
                        color: "#000000",
                        border: "3px solid #0096ff"
                    }
                }
            ];
            
            loadPDFWithReplacements(textReplacements);
        }

        function testCombined() {
            updateStatus('测试组合替换：多种长度文本同时替换');
            
            const textReplacements = [
                {
                    page: 1,
                    originalText: "JavaScript",
                    replacementText: "JS",
                    style: {
                        backgroundColor: "rgba(255, 255, 0, 0.8)",
                        color: "#000000",
                        border: "1px solid #ffff00"
                    }
                },
                {
                    page: 1,
                    originalText: "Dynamic languages",
                    replacementText: "动态语言",
                    style: {
                        backgroundColor: "rgba(0, 255, 0, 0.6)",
                        color: "#000000",
                        border: "1px solid #00ff00"
                    }
                },
                {
                    page: 1,
                    originalText: "trace compilation",
                    replacementText: "跟踪编译",
                    style: {
                        backgroundColor: "rgba(255, 0, 255, 0.5)",
                        color: "#ffffff",
                        border: "1px solid #ff00ff"
                    }
                }
            ];
            
            loadPDFWithReplacements(textReplacements);
        }

        function clearAll() {
            updateStatus('清除所有文本替换');
            
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'clearTextReplacements'
                }, '*');
                
                updateStatus('已清除所有文本替换');
            } else {
                updateStatus('请先加载PDF文档');
            }
        }

        function loadOriginal() {
            updateStatus('加载原始PDF（无文本替换）');
            
            const url = `build/generic/web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf`;
            document.getElementById('pdfViewer').src = url;
        }

        function loadPDFWithReplacements(textReplacements) {
            const encodedReplacements = encodeURIComponent(JSON.stringify(textReplacements));
            const url = `build/generic/web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf&textReplacements=${encodedReplacements}`;
            
            document.getElementById('pdfViewer').src = url;
            updateStatus(`已加载PDF，应用了 ${textReplacements.length} 个文本替换`);
        }

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'textReplacementStatus') {
                updateStatus(`PDF查看器状态：${event.data.message}`);
            }
        });

        // 页面加载完成后的初始化
        window.onload = function() {
            updateStatus('长文本跨行替换测试页面已加载，请选择测试功能');
        };
    </script>
</body>
</html>
