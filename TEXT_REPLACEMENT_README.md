# PDF.js 文本替换功能

基于PDF.js开源代码实现的文本替换预览功能，特别针对iframe_full_features.html文件进行了扩展。

## 功能特性

- ✅ **URL参数支持**：通过URL参数传递文本替换配置
- ✅ **PostMessage通信**：支持iframe环境下的动态配置
- ✅ **精确文本定位**：基于PDF.js文本层进行精确定位
- ✅ **视觉替换效果**：在原文位置显示替换文字，带有明显视觉标识
- ✅ **响应式支持**：支持缩放、旋转等PDF变换操作
- ✅ **多样式支持**：支持自定义颜色、边框、字体等样式
- ✅ **批量处理**：支持同时处理多个文本替换
- ✅ **动态管理**：支持运行时添加、清除文本替换

## 快速开始

### 1. 构建项目

```bash
npx gulp clean
npx gulp generic
```

### 2. 基础使用

#### URL参数方式

```javascript
const textReplacements = [
    {
        page: 1,
        originalText: "JavaScript",
        replacementText: "TypeScript",
        style: {
            backgroundColor: "rgba(255, 255, 0, 0.3)",
            color: "#000000",
            border: "1px solid rgba(255, 255, 0, 0.8)"
        }
    }
];

const encodedReplacements = encodeURIComponent(JSON.stringify(textReplacements));
const url = `build/generic/web/viewer.html?file=document.pdf&textReplacements=${encodedReplacements}`;
```

#### iframe集成

```html
<iframe id="pdfViewer" src="iframe_full_features.html"></iframe>

<script>
// 通过postMessage发送配置
document.getElementById('pdfViewer').contentWindow.postMessage({
    type: 'textReplacements',
    replacements: textReplacements,
    pdfFile: 'document.pdf'
}, '*');
</script>
```

### 3. 测试功能

打开 `test_text_replacement.html` 文件进行功能测试：

- 基础文本替换测试
- 中文替换测试
- 样式效果测试
- 动态添加/清除测试
- PostMessage通信测试

## 配置参数

### 文本替换对象结构

```javascript
{
    page: 1,                    // 页码（必需）
    originalText: "原始文本",    // 要替换的原始文本（必需）
    replacementText: "替换文本", // 替换后的文本（必需）
    style: {                    // 样式配置（可选）
        backgroundColor: "rgba(255, 255, 0, 0.3)",
        color: "#000000",
        border: "1px solid rgba(255, 255, 0, 0.8)",
        borderRadius: "2px",
        fontWeight: "normal",
        fontSize: "inherit",
        fontFamily: "inherit"
    }
}
```

### 样式选项

| 属性 | 说明 | 默认值 |
|------|------|--------|
| `backgroundColor` | 背景颜色 | `rgba(255, 255, 0, 0.3)` |
| `color` | 文字颜色 | 继承原文 |
| `border` | 边框样式 | `1px solid rgba(255, 255, 0, 0.8)` |
| `borderRadius` | 圆角半径 | `2px` |
| `fontWeight` | 字体粗细 | 继承原文 |
| `fontSize` | 字体大小 | 继承原文 |
| `fontFamily` | 字体族 | 继承原文 |

## API接口

### URL参数

- `textReplacements`: JSON编码的文本替换配置数组

### PostMessage事件

#### 发送给iframe的消息

```javascript
// 设置文本替换
{
    type: 'textReplacements',
    replacements: [...],
    pdfFile: 'document.pdf'
}

// 添加单个替换
{
    type: 'addTextReplacement',
    replacement: {...}
}

// 清除所有替换
{
    type: 'clearTextReplacements'
}
```

#### 从iframe接收的消息

```javascript
// 状态更新
{
    type: 'textReplacementStatus',
    message: '状态信息',
    success: true/false
}
```

### JavaScript API

```javascript
// 通过iframe_full_features.html提供的全局API
window.PDFTextReplacementAPI = {
    setTextReplacements: function(pdfFile, textReplacements),
    addTextReplacement: function(replacement),
    clearTextReplacements: function()
};
```

## 技术实现

### 核心组件

1. **TextReplacementManager** (`web/text_replacement_manager.js`)
   - 解析URL参数中的文本替换配置
   - 管理页面级别的文本替换数据
   - 协调文本定位和渲染过程

2. **文本定位算法**
   - 利用PDF.js文本层API进行精确文本搜索
   - 获取文本元素的DOM位置和尺寸
   - 计算替换文字的精确位置

3. **视觉渲染系统**
   - 创建覆盖层遮盖原始文本
   - 在覆盖层上显示替换文字
   - 应用自定义样式和视觉标识

4. **响应式更新**
   - 监听PDF页面变换事件
   - 动态重新计算文本位置
   - 保持替换效果的准确性

### 集成点

- **PDFPageView**: 集成文本替换管理器
- **PDFViewer**: 传递文本替换管理器实例
- **app.js**: 初始化文本替换管理器
- **viewer.html**: 包含文本替换CSS样式

## 浏览器支持

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 性能考虑

- 文本替换数量建议控制在每页50个以内
- 大量替换可能影响页面渲染性能
- 支持按需加载和延迟渲染

## 故障排除

### 常见问题

1. **文本替换不显示**
   - 检查JSON格式是否正确
   - 确认页码和文本内容匹配
   - 查看浏览器控制台错误信息

2. **位置不准确**
   - 确保PDF文档已完全加载
   - 检查文本层是否正确渲染
   - 验证原始文本是否存在

3. **样式不生效**
   - 检查CSS属性名称和值
   - 确认样式优先级
   - 验证颜色格式

### 调试模式

在浏览器控制台中启用调试日志：

```javascript
localStorage.setItem('pdfjs.verbosity', '5');
```

## 示例

### 基础示例

```javascript
const replacements = [
    {
        page: 1,
        originalText: "Hello World",
        replacementText: "你好世界",
        style: {
            backgroundColor: "rgba(255, 255, 0, 0.3)",
            color: "#000000"
        }
    }
];
```

### 高级示例

```javascript
const replacements = [
    {
        page: 1,
        originalText: "JavaScript",
        replacementText: "TypeScript",
        style: {
            backgroundColor: "rgba(0, 123, 255, 0.2)",
            color: "#ffffff",
            border: "2px dashed rgba(0, 123, 255, 0.8)",
            borderRadius: "4px",
            fontWeight: "bold",
            fontSize: "1.1em"
        }
    },
    {
        page: 2,
        originalText: "performance",
        replacementText: "性能",
        style: {
            backgroundColor: "rgba(40, 167, 69, 0.3)",
            color: "#000000",
            border: "1px solid rgba(40, 167, 69, 0.8)"
        }
    }
];
```

## 许可证

本功能基于PDF.js项目开发，遵循Apache License 2.0许可证。
