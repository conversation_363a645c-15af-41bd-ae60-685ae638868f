<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>文本替换功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .test-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .test-info ul {
            margin-bottom: 0;
        }
        .test-info li {
            margin-bottom: 5px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PDF.js 文本替换功能测试</h1>
        
        <div class="test-info">
            <h3>测试说明</h3>
            <ul>
                <li><strong>基础测试</strong>：加载PDF并应用预设的文本替换（JavaScript → TypeScript, Mozilla → OpenAI）</li>
                <li><strong>中文替换</strong>：测试英文到中文的文本替换（trace → 追踪, monkey → 猴子, compilation → 编译）</li>
                <li><strong>样式测试</strong>：测试不同颜色和样式的文本替换效果</li>
                <li><strong>动态添加</strong>：通过JavaScript API动态添加新的文本替换</li>
                <li><strong>清除功能</strong>：清除所有文本替换，恢复原始文本</li>
            </ul>
        </div>

        <div class="controls">
            <div class="button-group">
                <button class="btn-primary" onclick="loadBasicTest()">基础测试</button>
                <button class="btn-success" onclick="loadChineseTest()">中文替换测试</button>
                <button class="btn-warning" onclick="loadStyleTest()">样式测试</button>
                <button class="btn-primary" onclick="addDynamicReplacement()">动态添加替换</button>
                <button class="btn-danger" onclick="clearAllReplacements()">清除所有替换</button>
            </div>
            <div class="button-group">
                <button class="btn-primary" onclick="loadOriginalPDF()">加载原始PDF</button>
                <button class="btn-success" onclick="testURLParams()">URL参数测试</button>
                <button class="btn-warning" onclick="testPostMessage()">PostMessage测试</button>
            </div>
        </div>

        <div id="status" class="status info">
            准备就绪，请选择测试功能
        </div>

        <iframe id="pdfViewer" src="about:blank"></iframe>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function loadBasicTest() {
            updateStatus('加载基础测试：Dynamic languages → TypeScript, 跨行文本替换测试', 'info');

            const textReplacements = [
                {
                    page: 1,
                    originalText: "Dynamic languages",
                    replacementText: "TypeScript11",
                    style: {
                        backgroundColor: "rgba(255, 255, 0, 1)",
                        color: "#000000"
                    }
                },
                {
                    page: 1,
                    originalText: "such as JavaScript are harder to compile than statically typed languages",
                    replacementText: "如JavaScript这样的动态语言比静态类型语言更难编译",
                    style: {
                        backgroundColor: "rgba(0, 255, 0, 0.5)",
                        color: "#000000",
                        border: "2px solid rgba(0, 255, 0, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "We present an alternative compilation technique for dynamic languages that identifies frequently executed loop traces at run-time and then generates machine code on the fly that is specialized for the actual dynamic types occurring on each path through the loop",
                    replacementText: "我们提出了一种针对动态语言的替代编译技术，该技术在运行时识别频繁执行的循环轨迹，然后动态生成专门针对循环中每个路径上实际动态类型的机器代码",
                    style: {
                        backgroundColor: "rgba(255, 0, 255, 0.4)",
                        color: "#ffffff",
                        border: "2px dashed rgba(255, 0, 255, 0.8)"
                    }
                }
            ];

            loadPDFWithReplacements(textReplacements);
        }

        function loadChineseTest() {
            updateStatus('加载中文替换测试：trace → 追踪, monkey → 猴子', 'info');
            
            const textReplacements = [
                {
                    page: 1,
                    originalText: "Dynamic languages",
                    replacementText: "追踪",
                    style: {
                        backgroundColor: "rgba(255, 192, 203, 0.3)",
                        color: "#000000",
                        border: "1px solid rgba(255, 192, 203, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "monkey",
                    replacementText: "猴子",
                    style: {
                        backgroundColor: "rgba(0, 150, 255, 0.3)",
                        color: "#ffffff",
                        border: "1px solid rgba(0, 150, 255, 0.8)"
                    }
                }
            ];
            
            loadPDFWithReplacements(textReplacements);
        }

        function loadStyleTest() {
            updateStatus('加载样式测试：不同颜色和边框样式', 'info');
            
            const textReplacements = [
                {
                    page: 1,
                    originalText: "compilation",
                    replacementText: "编译过程",
                    style: {
                        backgroundColor: "rgba(128, 0, 128, 0.4)",
                        color: "#ffffff",
                        border: "2px dashed rgba(128, 0, 128, 0.8)",
                        fontWeight: "bold"
                    }
                },
                {
                    page: 1,
                    originalText: "performance",
                    replacementText: "性能表现",
                    style: {
                        backgroundColor: "rgba(255, 165, 0, 0.3)",
                        color: "#000000",
                        border: "1px dotted rgba(255, 165, 0, 0.8)",
                        fontSize: "1.1em"
                    }
                }
            ];
            
            loadPDFWithReplacements(textReplacements);
        }

        function loadPDFWithReplacements(textReplacements) {
            const encodedReplacements = encodeURIComponent(JSON.stringify(textReplacements));
            const url = `build/generic/web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf&textReplacements=${encodedReplacements}`;
            
            document.getElementById('pdfViewer').src = url;
            updateStatus(`已加载PDF，应用了 ${textReplacements.length} 个文本替换`, 'success');
        }

        function addDynamicReplacement() {
            updateStatus('通过API动态添加文本替换', 'info');
            
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                const newReplacement = {
                    page: 1,
                    originalText: "dynamic",
                    replacementText: "动态",
                    style: {
                        backgroundColor: "rgba(255, 0, 0, 0.3)",
                        color: "#ffffff",
                        border: "1px solid rgba(255, 0, 0, 0.8)"
                    }
                };
                
                iframe.contentWindow.postMessage({
                    type: 'addTextReplacement',
                    replacement: newReplacement
                }, '*');
                
                updateStatus('已动态添加文本替换：dynamic → 动态', 'success');
            } else {
                updateStatus('请先加载PDF文档', 'error');
            }
        }

        function clearAllReplacements() {
            updateStatus('清除所有文本替换', 'info');
            
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'clearTextReplacements'
                }, '*');
                
                updateStatus('已清除所有文本替换', 'success');
            } else {
                updateStatus('请先加载PDF文档', 'error');
            }
        }

        function loadOriginalPDF() {
            updateStatus('加载原始PDF（无文本替换）', 'info');
            
            const url = `build/generic/web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf`;
            document.getElementById('pdfViewer').src = url;
            updateStatus('已加载原始PDF文档', 'success');
        }

        function testURLParams() {
            updateStatus('测试URL参数传递文本替换', 'info');
            
            const testReplacements = [
                {
                    page: 1,
                    originalText: "Dynamic languages",
                    replacementText: "网址",
                    style: {
                        backgroundColor: "rgba(0, 255, 255, 0.3)",
                        color: "#000000",
                        border: "1px solid rgba(0, 255, 255, 0.8)"
                    }
                }
            ];
            
            loadPDFWithReplacements(testReplacements);
        }

        function testPostMessage() {
            updateStatus('测试PostMessage通信', 'info');
            
            // 先加载原始PDF
            loadOriginalPDF();
            
            // 延迟发送PostMessage
            setTimeout(() => {
                const iframe = document.getElementById('pdfViewer');
                if (iframe && iframe.contentWindow) {
                    const messageReplacements = [
                        {
                            page: 1,
                            originalText: "message",
                            replacementText: "消息",
                            style: {
                                backgroundColor: "rgba(255, 20, 147, 0.3)",
                                color: "#ffffff",
                                border: "1px solid rgba(255, 20, 147, 0.8)"
                            }
                        }
                    ];
                    
                    iframe.contentWindow.postMessage({
                        type: 'textReplacements',
                        replacements: messageReplacements,
                        pdfFile: 'compressed.tracemonkey-pldi-09.pdf'
                    }, '*');
                    
                    updateStatus('已通过PostMessage发送文本替换配置', 'success');
                }
            }, 2000);
        }

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'textReplacementStatus') {
                updateStatus(`PDF查看器状态：${event.data.message}`, event.data.success ? 'success' : 'error');
            }
        });

        // 页面加载完成后的初始化
        window.onload = function() {
            updateStatus('文本替换功能测试页面已加载，请选择测试功能', 'info');
        };
    </script>
</body>
</html>
