/* Copyright 2024 PDF.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* Text Replacement Layer */
.text-replacement-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

/* Base Text Replacement Styles */
.text-replacement {
  position: absolute;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
}

/* Text Replacement Overlay (covers original text) */
.text-replacement-overlay {
  background-color: #ffffff;
  z-index: 1;
}

/* Text Replacement Text (shows replacement text) */
.text-replacement-text {
  background-color: #ffffff;
  color: #000000; 
  border-radius: 2px;
  z-index: 2;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 1px 2px;
}

/* Color Variants */
.text-replacement-yellow {
  background-color: rgba(255, 255, 0, 0.3);
  border-color: rgba(255, 255, 0, 0.8);
}

.text-replacement-green {
  background-color: rgba(0, 255, 0, 0.3);
  border-color: rgba(0, 255, 0, 0.8);
}

.text-replacement-blue {
  background-color: rgba(0, 150, 255, 0.3);
  border-color: rgba(0, 150, 255, 0.8);
}

.text-replacement-red {
  background-color: rgba(255, 0, 0, 0.3);
  border-color: rgba(255, 0, 0, 0.8);
}

.text-replacement-orange {
  background-color: rgba(255, 165, 0, 0.3);
  border-color: rgba(255, 165, 0, 0.8);
}

.text-replacement-purple {
  background-color: rgba(128, 0, 128, 0.3);
  border-color: rgba(128, 0, 128, 0.8);
}

.text-replacement-pink {
  background-color: rgba(255, 192, 203, 0.3);
  border-color: rgba(255, 192, 203, 0.8);
}

/* Intensity Variants */
.text-replacement-light {
  background-color: rgba(255, 255, 0, 0.2);
  border-color: rgba(255, 255, 0, 0.6);
}

.text-replacement-medium {
  background-color: rgba(255, 255, 0, 0.4);
  border-color: rgba(255, 255, 0, 0.9);
}

.text-replacement-strong {
  background-color: rgba(255, 255, 0, 0.6);
  border-color: rgba(255, 255, 0, 1.0);
}

/* Border Style Variants */
.text-replacement-dashed {
  border-style: dashed;
}

.text-replacement-dotted {
  border-style: dotted;
}

.text-replacement-thick {
  border-width: 2px;
}

.text-replacement-thin {
  border-width: 0.5px;
}

/* Animation Effects */
.text-replacement-pulse {
  animation: textReplacementPulse 2s infinite;
}

@keyframes textReplacementPulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.3;
  }
}

.text-replacement-fade-in {
  animation: textReplacementFadeIn 1s ease-in-out;
}

@keyframes textReplacementFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.text-replacement-highlight {
  animation: textReplacementHighlight 3s ease-in-out;
}

@keyframes textReplacementHighlight {
  0% {
    background-color: rgba(255, 255, 0, 0.3);
  }
  50% {
    background-color: rgba(255, 255, 0, 0.8);
  }
  100% {
    background-color: rgba(255, 255, 0, 0.3);
  }
}

/* Hover Effects */
.text-replacement-text:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* Focus and Accessibility */
.text-replacement-text[tabindex] {
  pointer-events: auto;
}

.text-replacement-text[tabindex]:focus {
  outline: 2px solid #0066cc;
  outline-offset: 2px;
}

/* Tooltip Support */
.text-replacement-text[title] {
  cursor: help;
  pointer-events: auto;
}

.text-replacement-text[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .text-replacement-text {
    border-width: 1.5px;
    padding: 2px 3px;
  }
}

@media (max-width: 480px) {
  .text-replacement-text {
    border-width: 2px;
    padding: 3px 4px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .text-replacement-text {
    background-color: rgba(255, 255, 0, 0.5);
    border-color: rgba(255, 255, 0, 1.0);
    border-width: 2px;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .text-replacement-overlay {
    background-color: #2a2a2a;
  }
  
  .text-replacement-text {
    background-color: rgba(255, 255, 100, 0.4);
    border-color: rgba(255, 255, 100, 0.9);
    color: #ffffff;
  }
  
  .text-replacement-yellow {
    background-color: rgba(255, 255, 100, 0.4);
    border-color: rgba(255, 255, 100, 0.9);
  }
  
  .text-replacement-green {
    background-color: rgba(100, 255, 100, 0.4);
    border-color: rgba(100, 255, 100, 0.9);
  }
  
  .text-replacement-blue {
    background-color: rgba(100, 200, 255, 0.4);
    border-color: rgba(100, 200, 255, 0.9);
  }
}

/* Print Styles */
@media print {
  .text-replacement-text {
    background-color: rgba(255, 255, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 0, 0.6) !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  .text-replacement-overlay {
    background-color: #ffffff !important;
  }
}

/* Special States */
.text-replacement-selected {
  background-color: rgba(0, 123, 255, 0.3);
  border-color: rgba(0, 123, 255, 0.8);
  box-shadow: 0 0 4px rgba(0, 123, 255, 0.5);
}

.text-replacement-error {
  background-color: rgba(220, 53, 69, 0.3);
  border-color: rgba(220, 53, 69, 0.8);
}

.text-replacement-success {
  background-color: rgba(40, 167, 69, 0.3);
  border-color: rgba(40, 167, 69, 0.8);
}

/* Text Alignment Helpers */
.text-replacement-center {
  justify-content: center;
  text-align: center;
}

.text-replacement-right {
  justify-content: flex-end;
  text-align: right;
}

.text-replacement-left {
  justify-content: flex-start;
  text-align: left;
}

/* Font Size Adjustments */
.text-replacement-small {
  font-size: 0.8em;
}

.text-replacement-large {
  font-size: 1.2em;
}

/* Shadow Effects */
.text-replacement-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.text-replacement-glow {
  box-shadow: 0 0 8px rgba(255, 255, 0, 0.6);
}
